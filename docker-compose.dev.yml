version: '3.8'

services:
  sw-search-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sw-search-agent-dev
    restart: unless-stopped
    ports:
      - "${HOST_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0.1}
      - DATABASE_URL=${DATABASE_URL}
    volumes:
      # Mount ai-context directory for runtime access
      - ./ai-context:/app/ai-context:ro
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/api/search/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "sw-search-agent.service=true"
    networks:
      - sw-search-agent-network

networks:
  sw-search-agent-network:
    driver: bridge
