# Override file for local development
# This file is automatically loaded by docker-compose
version: '3.8'

services:
  sw-search-agent:
    # Use development environment by default
    environment:
      - NODE_ENV=development
    # Mount source code for development (optional - uncomment for live reload)
    # volumes:
    #   - ./src:/app/src:ro
    #   - ./public:/app/public:ro
    #   - ./ai-context:/app/ai-context:ro
    # Override command for development mode (optional)
    # command: ["npm", "run", "dev"]
