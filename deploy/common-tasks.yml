- name: Copy files to the server
  synchronize:
    src: ../../
    dest: '{{ deploy_folder }}'
    rsync_opts:
      - '--no-motd'
      - '--exclude=.git'
      - '--exclude=deploy'
      - '--exclude=.gitlab-ci.yml'
      - '--delete'

- name: Create environment variables file
  copy:
    dest: '{{ deploy_folder }}/.env'
    content: |
      NODE_ENV={{NODE_ENV}}
      HOST_PORT={{HOST_PORT}}
      OPENAI_API_KEY={{OPENAI_API_KEY}}
      OPENAI_BASE_URL={{OPENAI_BASE_URL}}
      OPENAI_MODEL={{OPENAI_MODEL}}
      OPENAI_TEMPERATURE={{OPENAI_TEMPERATURE}}
      DATABASE_URL={{DATABASE_URL}}

- name: Build Docker image(s)
  shell: "{{ docker_compose_command }} -f {{ deploy_folder }}/{{ docker_compose_file }} --env-file {{ deploy_folder }}/.env build"
  register: build_output

- name: Show build output
  debug:
    msg: "{{ build_output.stdout_lines }}"

- name: Stop and remove application container(s)
  shell: "{{ docker_compose_command }} -f {{ deploy_folder }}/{{ docker_compose_file }} down"
  register: down_output

- name: Show down output
  debug:
    msg: "{{ down_output.stdout_lines }}"

- name: Run compose
  shell: "{{ docker_compose_command }} -f {{ deploy_folder }}/{{ docker_compose_file }} --env-file {{ deploy_folder }}/.env up -d"
  register: up_output

- name: Show up output
  debug:
    msg: "{{ up_output.stdout_lines }}"

- name: Clean unused images
  shell: docker image prune -a --filter label=sw-search-agent.service=true --force
  register: prune_output

- name: Show prune output
  debug:
    msg: "{{ prune_output.stdout_lines }}"
