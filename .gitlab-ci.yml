stages:
  - deploy

deploy:dev:
  stage: deploy
  only:
    refs:
      - development
      - main
  script:
    - >
      ansible-playbook -l sw-search-agent-dev deploy/deploy.dev.yml
      -e COMMIT_SHA=$CI_COMMIT_SHA
      -e NODE_ENV=development
      -e HOST_PORT=$DEV_HOST_PORT
      -e OPENAI_API_KEY=$DEV_OPENAI_API_KEY
      -e OPENAI_BASE_URL=$DEV_OPENAI_BASE_URL
      -e OPENAI_MODEL=$DEV_OPENAI_MODEL
      -e OPENAI_TEMPERATURE=$DEV_OPENAI_TEMPERATURE
      -e DATABASE_URL=$DEV_DATABASE_URL
