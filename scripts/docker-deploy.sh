#!/bin/bash

# SW Search Agent Docker Deployment Script
# This script helps deploy the application using Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
HOST_PORT="3000"
COMPOSE_FILE="docker-compose.yml"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --env ENVIRONMENT    Set environment (development|production) [default: development]"
    echo "  -p, --port PORT          Set host port [default: 3000]"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Deploy with defaults"
    echo "  $0 -e production -p 8080 # Deploy production on port 8080"
    echo "  $0 --env development     # Deploy development environment"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            HOST_PORT="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'development' or 'production'"
    exit 1
fi

# Set compose file based on environment
if [[ "$ENVIRONMENT" == "development" ]]; then
    COMPOSE_FILE="docker-compose.dev.yml"
fi

print_status "Starting SW Search Agent deployment..."
print_status "Environment: $ENVIRONMENT"
print_status "Host Port: $HOST_PORT"
print_status "Compose File: $COMPOSE_FILE"

# Check if .env file exists
if [[ ! -f ".env" ]]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [[ -f ".env.example" ]]; then
        cp .env.example .env
        print_warning "Please edit .env file with your configuration before continuing."
        exit 1
    else
        print_error ".env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Export environment variables
export NODE_ENV="$ENVIRONMENT"
export HOST_PORT="$HOST_PORT"

print_status "Building and starting containers..."

# Stop existing containers
docker-compose -f "$COMPOSE_FILE" down 2>/dev/null || true

# Build and start containers
if docker-compose -f "$COMPOSE_FILE" up --build -d; then
    print_status "Deployment successful!"
    print_status "Application is running at: http://localhost:$HOST_PORT"
    print_status "Health check: http://localhost:$HOST_PORT/api/search/health"
    
    # Show container status
    echo ""
    print_status "Container status:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    # Show logs
    echo ""
    print_status "Recent logs:"
    docker-compose -f "$COMPOSE_FILE" logs --tail=20
else
    print_error "Deployment failed!"
    exit 1
fi
