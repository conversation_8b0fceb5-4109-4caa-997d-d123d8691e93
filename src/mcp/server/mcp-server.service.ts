import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from "@nestjs/config";
import { MCPServerRegistry } from './mcp-server.registry';
import { MCPServerConfig, MCPClient, MCPTransport } from '../types/mcp.types';

const DB_CONNECTION_FAILURE_TIMEOUT_MS = 5000; // 5 seconds timeout before exit

@Injectable()
export class MCPServerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MCPServerService.name);

  constructor(
    private readonly serverRegistry: MCPServerRegistry,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing MCP Server Service');
    await this.startConfiguredServers();
  }

  async onModuleDestroy() {
    this.logger.log('Destroying MCP Server Service');
    await this.stopAllServers();
  }

  async startServer(serverId: string): Promise<boolean> {
    const server = this.serverRegistry.getServer(serverId);
    if (!server) {
      this.logger.error(`Server not found: ${serverId}`);
      return false;
    }

    try {
      this.serverRegistry.updateServerStatus(serverId, 'starting');

      // Create StdioClientTransport
      const transport = new StdioClientTransport({
        command: server.config.command,
        args: server.config.args || [],
        env: server.config.env,
        cwd: server.config.cwd,
      });

      // Create MCP Client
      const client = new Client(
        {
          name: `nestjs-mcp-client-${server.config.name}`,
          version: '1.0.0',
        },
        {
          capabilities: {
            tools: {},
          },
        }
      );

      // Handle transport errors
      transport.onclose = () => {
        this.logger.warn(`Transport closed for server: ${serverId}`);
        this.serverRegistry.updateServerStatus(serverId, 'stopped');
        this.handleServerFailure(serverId, 'Transport closed unexpectedly');
      };

      transport.onerror = (error) => {
        this.logger.error(`Transport error for server ${serverId}:`, error);
        this.serverRegistry.updateServerStatus(serverId, 'error');
        this.handleServerFailure(serverId, `Transport error: ${error.message || error}`);
      };

      // Connect client to transport
      await client.connect(transport);

      // Store references
      server.transport = transport as MCPTransport;
      server.client = client as MCPClient;

      this.serverRegistry.updateServerStatus(serverId, 'running');
      this.logger.log(`Started MCP server: ${serverId}`);

      return true;
    } catch (error) {
      this.logger.error(`Failed to start server ${serverId}:`, error);
      this.serverRegistry.updateServerStatus(serverId, 'error');

      // Check if this is a database connection error
      const errorMessage = error.message || error.toString();
      if (this.isDatabaseConnectionError(errorMessage)) {
        this.handleServerFailure(serverId, `Database connection failed: ${errorMessage}`);
      }

      return false;
    }
  }

  async stopServer(serverId: string): Promise<boolean> {
    const server = this.serverRegistry.getServer(serverId);
    if (!server || !server.client || !server.transport) {
      return false;
    }

    try {
      // Close the client connection
      await server.client.close();

      // Close the transport
      await server.transport.close();

      server.client = undefined;
      server.transport = undefined;

      this.serverRegistry.updateServerStatus(serverId, 'stopped');
      this.logger.log(`Stopped MCP server: ${serverId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error stopping server ${serverId}:`, error);
      return false;
    }
  }

  getServerClient(serverId: string): MCPClient | undefined {
    const server = this.serverRegistry.getServer(serverId);
    return server?.client;
  }

  private async startConfiguredServers(): Promise<void> {
    const mcpServersConfigs = this.configService.get<MCPServerConfig[]>('mcp');
    // Configure the PostgreSQL MCP server
    const startPromises = mcpServersConfigs.map(async (config) => {
      const serverId = this.serverRegistry.registerServer(config);
      return this.startServer(serverId);
    });

    const results = await Promise.allSettled(startPromises);
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        this.logger.error(`Failed to start server ${mcpServersConfigs[index].name}:`, result.reason);
      }
    });
  }

  private async stopAllServers(): Promise<void> {
    const servers = this.serverRegistry.getAllServers();
    const stopPromises = servers
      .filter(server => server.status === 'running')
      .map(server => this.stopServer(server.id));

    await Promise.allSettled(stopPromises);
  }

  /**
   * Check if the error indicates a database connection failure
   */
  private isDatabaseConnectionError(errorMessage: string): boolean {
    const dbErrorPatterns = [
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'connection refused',
      'connection timeout',
      'database connection',
      'could not connect',
      'connection failed',
      'authentication failed',
      'password authentication failed',
      'database does not exist',
      'role does not exist',
      'server closed the connection',
      'connection terminated',
    ];

    const lowerErrorMessage = errorMessage.toLowerCase();
    return dbErrorPatterns.some(pattern => lowerErrorMessage.includes(pattern));
  }

  /**
   * Handle server failure by logging and exiting the process after a timeout
   */
  private handleServerFailure(serverId: string, reason: string): void {
    this.logger.error(`Critical server failure for ${serverId}: ${reason}`);
    this.logger.error(`Container will exit in ${DB_CONNECTION_FAILURE_TIMEOUT_MS}ms to trigger restart`);

    // Give some time for the database to stabilize before restart
    setTimeout(() => {
      this.logger.error('Exiting process due to database connection failure');
      process.exit(1);
    }, DB_CONNECTION_FAILURE_TIMEOUT_MS);
  }
}
