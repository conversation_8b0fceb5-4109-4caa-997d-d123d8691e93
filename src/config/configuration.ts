export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',

  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL,
    model: process.env.OPENAI_MODEL,
    temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.1,
  },

  mcp: [
    {
      name: 'postgres',
      command: 'node',
      args: [
        './node_modules/@modelcontextprotocol/server-postgres/dist/index.js',
        process.env.DATABASE_URL,
      ],
    },
  ],
});
